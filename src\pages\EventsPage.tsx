import React, { useState, useEffect, useRef } from 'react';
import { useNavigate } from 'react-router-dom';
import { api } from '../utils/api';
import { Event, EventChannel } from '../types/events';
import { useTranslation } from '../hooks/useTranslation';
import { RETURN_KEY_CODE } from '../utils/keysCode';
import EventChannelsModal from '../components/EventChannelsModal';
import '../styles/events.css';

const EventsPage: React.FC = () => {
  const [events, setEvents] = useState<Event[]>([]);
  const [eventsChannels, setEventsChannels] = useState<{ [eventId: string]: EventChannel[] }>({});
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedIndex, setSelectedIndex] = useState(0);
  const [modalOpen, setModalOpen] = useState(false);
  const [selectedEvent, setSelectedEvent] = useState<Event | null>(null);
  const navigate = useNavigate();
  const { t } = useTranslation();
  const containerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    fetchEvents();
  }, []);

  const fetchEvents = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await api.fetchEvents();

      if (typeof response === 'string') {
        setError(response);
        return;
      }

      if (response && response.data && response.data.events) {
        const eventsList = response.data.events;
        setEvents(eventsList);

        // Récupérer les chaînes pour chaque événement
        await fetchAllEventsChannels(eventsList);
      } else {
        setError('No events data received');
      }
    } catch (err) {
      setError('Failed to load events. Please try again later.');
      console.error('Error fetching events:', err);
    } finally {
      setLoading(false);
    }
  };

  const fetchAllEventsChannels = async (eventsList: Event[]) => {
    const channelsData: { [eventId: string]: EventChannel[] } = {};

    // Récupérer les chaînes pour chaque événement en parallèle
    const promises = eventsList.map(async (event) => {
      try {
        const response = await api.fetchEventsChannels(event.id);
        if (typeof response !== 'string' && response?.data?.channels) {
          channelsData[event.id] = response.data.channels;
        }
      } catch (error) {
        console.error(`Error fetching channels for event ${event.id}:`, error);
        channelsData[event.id] = [];
      }
    });

    await Promise.all(promises);
    setEventsChannels(channelsData);
  };

  const handleKeyDown = (event: KeyboardEvent) => {
    // Si le modal est ouvert, ne pas traiter les événements clavier dans EventsPage
    if (modalOpen || loading || events.length === 0) return;

    switch (event.keyCode) {
      case 38: // Up arrow
        event.preventDefault();
        setSelectedIndex(prev => Math.max(0, prev - 1));
        break;
      case 40: // Down arrow
        event.preventDefault();
        setSelectedIndex(prev => Math.min(events.length - 1, prev + 1));
        break;
      case 13: // Enter
        event.preventDefault();
        handleEventSelect(events[selectedIndex]);
        break;
      case RETURN_KEY_CODE:
      case 10009: // Backspace
        event.preventDefault();
        navigate("/?section=multipacks&item=events");
        break;
    }
  };
  const handleEventSelect = (event: Event) => {
    setSelectedEvent(event);
    setModalOpen(true);
  };

  const handleCloseModal = () => {
    setModalOpen(false);
    setSelectedEvent(null);
  };

  useEffect(() => {
    window.addEventListener('keydown', handleKeyDown);
    return () => {
      window.removeEventListener('keydown', handleKeyDown);
    };
  }, [loading, events, selectedIndex, modalOpen]);

  // Auto-scroll to selected item
  useEffect(() => {
    if (containerRef.current && events.length > 0) {
      const selectedElement = containerRef.current.children[selectedIndex] as HTMLElement;
      if (selectedElement) {
        selectedElement.scrollIntoView({
          behavior: 'smooth',
          block: 'center'
        });
      }
    }
  }, [selectedIndex]);

  const formatDate = (dateString: string) => {
    try {
      const date = new Date(dateString);
      return date.toLocaleDateString('fr-FR', {
        day: '2-digit',
        month: '2-digit',
        year: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      });
    } catch {
      return dateString;
    }
  };

  const formatDuration = (duration: string) => {
    const minutes = parseInt(duration);
    if (isNaN(minutes)) return duration;
    
    const hours = Math.floor(minutes / 60);
    const remainingMinutes = minutes % 60;
    
    if (hours > 0) {
      return `${hours}h ${remainingMinutes}min`;
    }
    return `${minutes}min`;
  };

  if (loading) {
    return (
      <div className="flex h-screen bg-background text-white">
        <div className="w-1/4 bg-sidebar flex items-center justify-center">
          <div className="text-4xl">{t('events')}</div>
        </div>
        <div className="flex-1 flex items-center justify-center">
          <div className="text-2xl">{t('loading')}</div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex h-screen bg-background text-white">
        <div className="w-1/4 bg-sidebar flex items-center justify-center">
          <div className="text-4xl">{t('events')}</div>
        </div>
        <div className="flex-1 flex items-center justify-center">
          <div className="text-center">
            <div className="text-2xl text-red-500 mb-4">{error}</div>
            <button 
              onClick={fetchEvents}
              className="px-6 py-3 bg-blue-600 text-white rounded hover:bg-blue-700"
            >
              {t('retry')}
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="flex h-screen bg-background text-white">
      <div className="w-1/4 bg-sidebar flex items-center justify-center">
        <div className="text-4xl">{t('events')}</div>
      </div>
      
      <div className="flex-1 p-8 mt-[125px]">
        {events.length === 0 ? (
          <div className="flex items-center justify-center h-full">
            <div className="text-4xl text-gray-400">{t('no_events')}</div>
          </div>
        ) : (
          <div ref={containerRef} className="space-y-6 max-h-full overflow-y-auto pr-1">
            {events.map((event, index) => (
              <div
                key={event.id}
                className={`event-card glass-shine relative flex items-center p-8 pr-14 rounded-2xl border-2 ${
                  selectedIndex === index
                    ? 'selected border-red-500 tv-shadow-selected'
                    : 'border-transparent tv-shadow '
                }`}
                style={{
                  background: selectedIndex === index
                    ? 'linear-gradient(135deg, rgba(239, 68, 68, 0.2), rgba(239, 68, 68, 0.1))'
                    : 'linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05))'
                }}
              >
                <div className="flex items-center space-x-8 flex-1">
                  {/* Logos des équipes */}
                  <div className="flex items-center space-x-6">
                    <img
                      src={event.logoL}
                      alt="Team 1"
                      className="w-24 h-24 object-contain drop-shadow-lg"
                      onError={(e) => {
                        (e.target as HTMLImageElement).src = '/NoLogo.png';
                      }}
                    />
                    <span className="text-4xl font-bold text-white tv-text-bright">VS</span>
                    <img
                      src={event.logoR}
                      alt="Team 2"
                      className="w-24 h-24 object-contain drop-shadow-lg"
                      onError={(e) => {
                        (e.target as HTMLImageElement).src = '/NoLogo.png';
                      }}
                    />
                  </div>

                  {/* Informations de l'événement */}
                  <div className="flex-1">
                    <h3 className="text-3xl font-bold mb-3 text-white tv-text-bright">{event.name}</h3>
                    <p className="text-xl text-gray-200 mb-2 tv-text">{event.competition}</p>
                    <div className="flex items-center space-x-6 text-lg text-gray-300">
                      <span className="tv-text">{formatDate(event.start)}</span>
                      <span>•</span>
                      <span className="tv-text">{formatDuration(event.duration)}</span>
                    </div>
                  </div>
                </div>

                {/* Chaînes disponibles à droite */}
                {eventsChannels[event.id] && eventsChannels[event.id].length > 0 && (
                  <div className="flex flex-col space-y-4 mr-16">
                    <span className="text-xl text-gray-300 font-medium drop-shadow-md">
                      {eventsChannels[event.id].length} {t('channels_available')}
                    </span>
                    <div className="flex gap-2">
                      {eventsChannels[event.id].slice(0, 6).map((channel) => (
                        <img
                          key={channel.id}
                          src={channel.logo}
                          alt={channel.name}
                          className="w-14 h-14 object-contain rounded-lg bg-white/20 p-1 drop-shadow-md"
                          title={channel.name}
                          onError={(e) => {
                            (e.target as HTMLImageElement).src = '/NoLogo.png';
                          }}
                        />
                      ))}
                      {eventsChannels[event.id].length > 6 && (
                        <div className="w-14 h-14 rounded-lg bg-white/20 flex items-center justify-center">
                          <span className="text-xl text-white font-bold">+{eventsChannels[event.id].length - 6}</span>
                        </div>
                      )}
                    </div>
                  </div>
                )}

                {/* Indicateur de sélection */}
                {selectedIndex === index && (
                  <div className="absolute right-6 top-1/2 transform -translate-y-1/2">
                    <div
                      className="flex items-center justify-center"
                      style={{
                        width: '56px',
                        height: '56px',
                        borderRadius: '50%',
                        background: 'radial-gradient(circle, #ef4444 80%, #fff0 100%)',
                        boxShadow: '0 0 16px 4px rgba(239,68,68,0.5), 0 2px 8px rgba(0,0,0,0.2)',
                        animation: 'pulse-red 1.5s infinite',
                        border: '3px solid #fff3',
                      }}
                    >
                      <i className="fas fa-play text-white text-3xl" style={{ filter: 'drop-shadow(0 0 6px #fff)' }}></i>
                    </div>
                  </div>
                )}
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Modal des chaînes d'événements */}
      {selectedEvent && (
        <EventChannelsModal
          eventId={selectedEvent.id}
          eventName={selectedEvent.name}
          isOpen={modalOpen}
          onClose={handleCloseModal}
        />
      )}
    </div>
  );
};

export default EventsPage;
