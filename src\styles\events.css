/* Styles pour les événements avec effet glass */

/* Animation pour les cartes d'événements */
.event-card {
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.event-card.selected {
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  box-shadow: 0 25px 50px -12px rgba(239, 68, 68, 0.25);
}

/* Effet de brillance pour les éléments sélectionnés */
.glass-shine {
  position: relative;
  overflow: hidden;
}

.glass-shine::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  transition: left 0.5s;
}

.glass-shine:hover::before {
  left: 100%;
}

/* Amélioration des ombres pour la profondeur */
.tv-shadow {
  box-shadow: 
    0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06),
    0 0 0 1px rgba(255, 255, 255, 0.05);
}

.tv-shadow-selected {
  box-shadow:
    0 25px 50px -12px rgba(239, 68, 68, 0.25),
    0 0 0 3px rgba(239, 68, 68, 0.6),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
}


/* Animation d'entrée pour le modal */
@keyframes modal-enter {
  from {
    opacity: 0;
    transform: scale(0.95) translateY(-10px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

.modal-enter {
  animation: modal-enter 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Amélioration du contraste pour la lisibilité TV */
.tv-text {
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
}

.tv-text-bright {
  text-shadow: 
    0 2px 4px rgba(0, 0, 0, 0.8),
    0 0 8px rgba(255, 255, 255, 0.1);
}
